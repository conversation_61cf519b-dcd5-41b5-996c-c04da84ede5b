"use client"

import React, { useState, useTransition } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import type { AppraisalDetails, EmployeeDetails } from "@/lib/types"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Send, Loader, CheckCircle, AlertCircle, AlertTriangle, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "lucide-react"
import { useAuto<PERSON>ve } from "@/hooks/use-autosave"
import { saveAppraisalDraftAction, submitAppraisalAction, createAppraisalRevisionAction, resubmitAppraisalRevisionAction } from "@/lib/actions"
import { appraisalSubmissionSchema, type AppraisalSubmission } from "@/lib/schemas"
import {
  announceToScreenReader,
  announceFormError,
  announceFormSuccess,
  generateId
} from "@/lib/accessibility"

const appraisalQuestions = {
  q1: "Overall performance this month:",
  q2: "Completed all assigned tasks?",
  q3: "Primary project/focus area:",
  q4: "Achievements and areas for improvement:",
  q5: "Next month's goals and focus:",
}

type AppraisalFormProps = {
  employee: EmployeeDetails
  appraisal: AppraisalDetails | null
}

function AutosaveStatusIndicator({
  autosave
}: {
  autosave: ReturnType<typeof useAutosave>
}) {
  const { status, retryCount, isChanged, saveNow } = autosave

  // Announce status changes to screen readers
  React.useEffect(() => {
    switch (status) {
      case "saving":
        announceToScreenReader("Saving draft", "polite")
        break
      case "success":
        announceToScreenReader("Draft saved successfully", "polite")
        break
      case "error":
        announceToScreenReader("Failed to save draft", "assertive")
        break
    }
  }, [status])

  switch (status) {
    case "saving":
      return (
        <div
          className="flex items-center gap-2 text-sm text-muted-foreground"
          role="status"
          aria-live="polite"
        >
          <Loader className="h-4 w-4 animate-spin" aria-hidden="true" />
          <span>Saving draft...</span>
        </div>
      )
    case "retrying":
      return (
        <div
          className="flex items-center gap-2 text-sm text-yellow-600"
          role="status"
          aria-live="polite"
        >
          <Loader className="h-4 w-4 animate-spin" aria-hidden="true" />
          <span>Retrying... (attempt {retryCount + 1})</span>
        </div>
      )
    case "success":
      return (
        <div
          className="flex items-center gap-2 text-sm text-green-600"
          role="status"
          aria-live="polite"
        >
          <CheckCircle className="h-4 w-4" aria-hidden="true" />
          <span>Draft saved</span>
        </div>
      )
    case "error":
      return (
        <div
          className="flex items-center gap-2 text-sm text-red-600"
          role="alert"
          aria-live="assertive"
        >
          <AlertCircle className="h-4 w-4" aria-hidden="true" />
          <span>Save failed</span>
          <button
            onClick={saveNow}
            className="text-xs underline hover:no-underline"
            aria-label="Retry saving draft now"
          >
            Retry now
          </button>
        </div>
      )
    case "idle":
      if (isChanged) {
        return (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span>Unsaved changes</span>
          </div>
        )
      }
      return <div className="h-6" /> // Placeholder for idle state
    default:
      return <div className="h-6" />
  }
}

export function AppraisalForm({ employee, appraisal }: AppraisalFormProps) {
  const [isPending, startTransition] = useTransition()
  const [submitError, setSubmitError] = useState<string | null>(null)
  const [submitSuccess, setSubmitSuccess] = useState(false)
  const [isCreatingRevision, setIsCreatingRevision] = useState(false)
  const [revisionError, setRevisionError] = useState<string | null>(null)

  // Form for draft data (less strict validation)
  const [formData, setFormData] = useState<Partial<AppraisalDetails>>(() => {
    // Safely extract only the needed fields to avoid circular references
    const safeAppraisal = appraisal ? {
      q1: appraisal.q1 ?? null,
      q2: appraisal.q2 ?? false,
      q3: appraisal.q3 ?? "",
      q4: appraisal.q4 ?? "",
      q5: appraisal.q5 ?? "",
    } : {
      q1: null,
      q2: false,
      q3: "",
      q4: "",
      q5: "",
    }

    console.log('🔄 [DEBUG] Initializing form data:', safeAppraisal)
    return safeAppraisal
  })

  // Form for submission (strict validation)
  const submissionForm = useForm<Omit<AppraisalSubmission, 'id' | 'periodId' | 'employeeId' | 'managerId' | 'status'>>({
    resolver: zodResolver(appraisalSubmissionSchema.omit({
      id: true,
      periodId: true,
      employeeId: true,
      managerId: true,
      status: true
    })),
    defaultValues: {
      q1: appraisal?.q1 ?? undefined,
      q2: Boolean(appraisal?.q2) ?? false, // Ensure boolean type
      q3: String(appraisal?.q3 ?? ""), // Ensure string type
      q4: String(appraisal?.q4 ?? ""), // Ensure string type
      q5: String(appraisal?.q5 ?? ""), // Ensure string type
    },
  })

  // Sync form data with submission form
  React.useEffect(() => {
    submissionForm.reset({
      q1: formData.q1 || undefined,
      q2: formData.q2,
      q3: formData.q3,
      q4: formData.q4,
      q5: formData.q5,
    })
  }, [formData, submissionForm])

  const autosave = useAutosave({
    data: formData,
    onSave: async (data, signal) => {
      // Add employee context to the draft data
      const draftData = {
        ...data,
        employeeId: employee.id,
        periodId: appraisal?.periodId || '', // Will be set by server action
        managerId: '', // Will be set by server action from auth
      }
      return saveAppraisalDraftAction(draftData)
    },
    interval: 3000, // Save every 3 seconds
    maxRetries: 3,
    enabled: !submitSuccess && (appraisal?.status !== 'submitted' || appraisal?.isRevision),
    onError: (error, retryCount) => {
      console.error(`Autosave failed (attempt ${retryCount}):`, error)
    },
    onSuccess: () => {
      console.log('Draft saved successfully')
    },
  })

  const handleInputChange = (
    field: keyof Omit<AppraisalDetails, "id" | "periodId" | "employeeId" | "managerId" | "status">,
    value: any,
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
    // Also update the submission form and trigger validation
    submissionForm.setValue(field as any, value, { shouldValidate: true })
  }

  const handleSubmit = async () => {
    setSubmitError(null)

    // Validate the form before submission
    const isValid = await submissionForm.trigger()
    if (!isValid) {
      const errors = submissionForm.formState.errors
      console.log('Form validation errors:', errors)
      setSubmitError('Please fill in all required fields correctly before submitting.')
      return
    }

    startTransition(async () => {
      try {
        const submissionData = {
          ...formData,
          employeeId: employee.id,
          periodId: appraisal?.periodId || '',
          managerId: '', // Will be set by server action from auth
          status: 'submitted' as const,
          // Only include id if it exists (for revisions)
          ...(appraisal?.id && { id: appraisal.id }),
        }

        try {
          console.log('📋 [DEBUG] Form submission data:', JSON.stringify(submissionData, null, 2))
        } catch (jsonError) {
          console.log('📋 [DEBUG] Form submission data (raw):', submissionData)
          console.log('📋 [DEBUG] JSON stringify error:', jsonError)
        }
        console.log('📋 [DEBUG] Is revision?', appraisal?.isRevision)
        console.log('📋 [DEBUG] Appraisal ID:', appraisal?.id)

        // Use resubmit action if this is a revision, otherwise use regular submit
        const result = appraisal?.isRevision
          ? await resubmitAppraisalRevisionAction(submissionData)
          : await submitAppraisalAction(submissionData)

        if (result.success) {
          setSubmitSuccess(true)
          setSubmitError(null)
          // Force page refresh to update appraisal status
          if (typeof window !== 'undefined') {
            window.location.reload()
          }
        } else {
          setSubmitError('error' in result ? result.error : 'Failed to submit appraisal')
        }
      } catch (error) {
        console.error('Submission error:', error)
        setSubmitError('An unexpected error occurred. Please try again.')
      }
    })
  }

  const handleCreateRevision = async () => {
    if (!appraisal?.id) return
    
    setRevisionError(null)
    setIsCreatingRevision(true)

    try {
      const result = await createAppraisalRevisionAction(appraisal.id)
      
      if (result.success) {
        // Force page refresh to load the revision
        if (typeof window !== 'undefined') {
          window.location.reload()
        }
      } else {
        setRevisionError('error' in result ? result.error : 'Failed to create revision')
      }
    } catch (error) {
      console.error('Revision creation error:', error)
      setRevisionError('An unexpected error occurred. Please try again.')
    } finally {
      setIsCreatingRevision(false)
    }
  }

  const isSubmitted = appraisal?.status === "submitted" && !appraisal?.isRevision

  return (
    <div className="grid gap-6">
      {/* Success Alert */}
      {submitSuccess && (
        <Alert
          className="border-green-200 bg-green-50"
          role="alert"
          aria-live="polite"
        >
          <CheckCircle className="h-4 w-4 text-green-600" aria-hidden="true" />
          <AlertDescription className="text-green-800">
            Appraisal submitted successfully! The form is now read-only.
          </AlertDescription>
        </Alert>
      )}

      {/* Error Alert */}
      {submitError && (
        <Alert
          variant="destructive"
          role="alert"
          aria-live="assertive"
        >
          <AlertTriangle className="h-4 w-4" aria-hidden="true" />
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      {/* Revision Error Alert */}
      {revisionError && (
        <Alert
          variant="destructive"
          role="alert"
          aria-live="assertive"
        >
          <AlertTriangle className="h-4 w-4" aria-hidden="true" />
          <AlertDescription>{revisionError}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                Monthly Review
                {appraisal?.isRevision && (
                  <span className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded-full">
                    <RotateCcw className="h-3 w-3" />
                    Revision {appraisal.revisionNumber}
                  </span>
                )}
              </CardTitle>
              <CardDescription>
                Fill out the form below for {employee.fullName}. Your progress is saved as a draft automatically.
                {appraisal?.isRevision && appraisal.originalSubmissionDate && (
                  <div className="flex items-center gap-1 mt-1 text-sm text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    Originally submitted: {appraisal.originalSubmissionDate.split('T')[0]}
                  </div>
                )}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-8">
          {/* Questions... (content is unchanged) */}
          <fieldset className="space-y-2">
            <legend className="text-sm font-medium">
              {appraisalQuestions.q1} <span className="text-red-500" aria-label="required">*</span>
            </legend>
            <RadioGroup
              value={formData.q1 ?? ""}
              onValueChange={(value) => handleInputChange("q1", value as AppraisalDetails["q1"])}
              className="flex flex-col space-y-2 pt-2 sm:flex-row sm:space-y-0 sm:space-x-4"
              disabled={isSubmitted || submitSuccess}
              aria-required="true"
              aria-describedby={submissionForm.formState.errors.q1 ? "q1-error" : undefined}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="below-expectations" id="q1-below" />
                <Label htmlFor="q1-below">Below Expectations</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="meets-expectations" id="q1-meets" />
                <Label htmlFor="q1-meets">Meets Expectations</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="exceeds-expectations" id="q1-exceeds" />
                <Label htmlFor="q1-exceeds">Exceeds Expectations</Label>
              </div>
            </RadioGroup>
            {submissionForm.formState.errors.q1 && (
              <p
                id="q1-error"
                className="text-sm text-red-500"
                role="alert"
                aria-live="polite"
              >
                {submissionForm.formState.errors.q1.message}
              </p>
            )}
          </fieldset>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="q2"
              checked={formData.q2}
              onCheckedChange={(checked) => handleInputChange("q2", !!checked)}
              disabled={isSubmitted}
            />
            <Label
              htmlFor="q2"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              {appraisalQuestions.q2}
            </Label>
          </div>
          <div className="space-y-2">
            <Label htmlFor="q3">{appraisalQuestions.q3} <span className="text-red-500">*</span></Label>
            <Input
              id="q3"
              value={formData.q3}
              onChange={(e) => handleInputChange("q3", e.target.value)}
              placeholder="e.g., Project Phoenix, Q3 Marketing Campaign"
              disabled={isSubmitted || submitSuccess}
            />
            {submissionForm.formState.errors.q3 && (
              <p className="text-sm text-red-500">{submissionForm.formState.errors.q3.message}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="q4">{appraisalQuestions.q4} <span className="text-red-500">*</span></Label>
            <Textarea
              id="q4"
              value={formData.q4}
              onChange={(e) => handleInputChange("q4", e.target.value)}
              placeholder="Summarize key accomplishments and any challenges faced..."
              rows={5}
              disabled={isSubmitted || submitSuccess}
            />
            {submissionForm.formState.errors.q4 && (
              <p className="text-sm text-red-500">{submissionForm.formState.errors.q4.message}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="q5">{appraisalQuestions.q5} <span className="text-red-500">*</span></Label>
            <Input
              id="q5"
              value={formData.q5}
              onChange={(e) => handleInputChange("q5", e.target.value)}
              placeholder="e.g., Lead design for new feature, improve API documentation"
              disabled={isSubmitted || submitSuccess}
            />
            {submissionForm.formState.errors.q5 && (
              <p className="text-sm text-red-500">{submissionForm.formState.errors.q5.message}</p>
            )}
          </div>
        </CardContent>
        <CardFooter className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {!isSubmitted && !submitSuccess && (
              <AutosaveStatusIndicator autosave={autosave} />
            )}
          </div>

          <div className="flex items-center gap-4">
            {isSubmitted || submitSuccess ? (
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2 text-sm text-green-600">
                  <CheckCircle className="h-4 w-4" />
                  <span>
                    Appraisal {appraisal?.isRevision ? 'revision ' : ''}submitted successfully
                    {appraisal?.revisionNumber && appraisal.revisionNumber > 1 && (
                      <span className="ml-1">(v{appraisal.revisionNumber})</span>
                    )}
                  </span>
                </div>
                {!appraisal?.isRevision && (
                  <Button
                    onClick={handleCreateRevision}
                    disabled={isCreatingRevision}
                    variant="outline"
                    size="sm"
                    className="min-w-[140px]"
                  >
                    {isCreatingRevision ? (
                      <>
                        <Loader className="mr-2 h-4 w-4 animate-spin" /> Creating...
                      </>
                    ) : (
                      <>
                        <Edit3 className="mr-2 h-4 w-4" /> Edit Appraisal
                      </>
                    )}
                  </Button>
                )}
              </div>
            ) : (
              <>
                <p className="text-xs text-muted-foreground">
                  <span className="text-red-500">*</span> Required fields
                </p>
                <Button
                  onClick={handleSubmit}
                  disabled={isPending}
                  className="min-w-[140px]"
                >
                  {isPending ? (
                    <>
                      <Loader className="mr-2 h-4 w-4 animate-spin" /> 
                      {appraisal?.isRevision ? 'Resubmitting...' : 'Submitting...'}
                    </>
                  ) : (
                    <>
                      <Send className="mr-2 h-4 w-4" /> 
                      {appraisal?.isRevision ? 'Resubmit Revision' : 'Submit Appraisal'}
                    </>
                  )}
                </Button>
              </>
            )}
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
