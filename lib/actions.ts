"use server"

import { revalidatePath } from "next/cache"
// import { auth } from "@clerk/nextjs/server"
import { saveDepartment, saveEmployee, savePeriod, getPeriods, invalidatePerformanceStatsCache } from "./data"
import { db } from "./db"
import {
  validateDepartment,
  validateEmployee,
  validateAppraisal,
  validateAppraisalSubmission,
  validatePeriod,
  departmentFormSchema,
  employeeFormSchema,
  appraisalPeriodFormSchema,
  appraisalFormSchema
} from "./schemas"
import {
  requirePermission,
  checkRateLimit,
  logUserAction,
  validateSession
} from "./auth"
import type { AppraisalDetails, Department, Employee, AppraisalPeriod } from "./types"

// Error classes for better error handling
class AuthenticationError extends Error {
  constructor(message = "Authentication required") {
    super(message)
    this.name = "AuthenticationError"
  }
}

class AuthorizationError extends Error {
  constructor(message = "Insufficient permissions") {
    super(message)
    this.name = "AuthorizationError"
  }
}

class ValidationError extends Error {
  constructor(message = "Validation failed") {
    super(message)
    this.name = "ValidationError"
  }
}

class RateLimitError extends Error {
  constructor(message = "Rate limit exceeded") {
    super(message)
    this.name = "RateLimitError"
  }
}

// Helper function to handle server action errors
function handleServerActionError(error: unknown) {
  console.error("Server action error:", error)

  if (error instanceof AuthenticationError) {
    return { success: false, error: "Authentication required", code: "AUTH_REQUIRED" }
  }

  if (error instanceof AuthorizationError) {
    return { success: false, error: "Insufficient permissions", code: "INSUFFICIENT_PERMISSIONS" }
  }

  if (error instanceof ValidationError) {
    return { success: false, error: error.message, code: "VALIDATION_ERROR" }
  }

  if (error instanceof RateLimitError) {
    return { success: false, error: "Too many requests. Please try again later.", code: "RATE_LIMIT" }
  }

  // Generic error for unexpected issues
  return {
    success: false,
    error: "An unexpected error occurred. Please try again.",
    code: "INTERNAL_ERROR"
  }
}

export async function saveDepartmentAction(formData: FormData) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'department-save', 10, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('department:write')

    // Extract and validate data
    const rawData = {
      id: formData.get('id') as string || undefined,
      name: formData.get('name') as string,
    }

    const validatedData = departmentFormSchema.parse(rawData)

    // Log the action
    await logUserAction('department:save', { departmentName: validatedData.name })

    // Save to database
    await saveDepartment(validatedData)

    // Revalidate cache
    revalidatePath("/dashboard/departments")

    return { success: true, message: "Department saved successfully." }
  } catch (error) {
    return handleServerActionError(error)
  }
}

export async function saveEmployeeAction(formData: FormData) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'employee-save', 10, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('employee:write')

    // Extract and validate data
    const rawData = {
      id: formData.get('id') as string || undefined,
      fullName: formData.get('fullName') as string,
      compensation: formData.get('compensation') as 'hourly' | 'monthly',
      rate: Number(formData.get('rate')),
      departmentId: formData.get('departmentId') as string,
      managerId: formData.get('managerId') as string || null,
      active: formData.get('active') === 'true',
    }

    const validatedData = employeeFormSchema.parse(rawData)

    // Log the action
    await logUserAction('employee:save', {
      employeeName: validatedData.fullName,
      isUpdate: !!rawData.id
    })

    // Save to database
    await saveEmployee(validatedData)

    // Revalidate cache
    revalidatePath("/dashboard/employees")

    return { success: true, message: "Employee saved successfully." }
  } catch (error) {
    return handleServerActionError(error)
  }
}

export async function savePeriodAction(formData: FormData) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'period-save', 5, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('period:write')

    // Extract and validate data
    const rawData = {
      id: formData.get('id') as string || undefined,
      periodStart: formData.get('periodStart') as string,
      periodEnd: formData.get('periodEnd') as string,
      closed: formData.get('closed') === 'true',
    }

    const validatedData = appraisalPeriodFormSchema.parse(rawData)

    // Log the action
    await logUserAction('period:save', {
      periodStart: validatedData.periodStart,
      periodEnd: validatedData.periodEnd,
      isUpdate: !!rawData.id
    })

    // Save to database
    await savePeriod(validatedData)

    // Revalidate cache
    revalidatePath("/dashboard/periods")

    return { success: true, message: "Appraisal Period saved successfully." }
  } catch (error) {
    return handleServerActionError(error)
  }
}

export async function saveAppraisalDraftAction(draft: Partial<AppraisalDetails>) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting (more generous for autosave)
    if (!checkRateLimit(session.userId, 'appraisal-draft', 30, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('appraisal:write')

    // Validate draft data (less strict than submission)
    const validatedData = appraisalFormSchema.partial().parse(draft)

    // Get current period
    const periods = await getPeriods()
    const currentPeriod = periods.find(p => !p.closed)

    if (!currentPeriod) {
      throw new ValidationError("No active appraisal period found")
    }

    // Ensure we have required fields for draft
    if (!draft.employeeId) {
      throw new ValidationError("Employee ID is required")
    }

    // Log the action (less verbose for drafts)
    console.log(`Saving draft for user ${session.userId}`, {
      employeeId: draft.employeeId,
      hasQ1: !!draft.q1,
      hasQ2: draft.q2 !== undefined,
      hasQ3: !!draft.q3,
      hasQ4: !!draft.q4,
      hasQ5: !!draft.q5,
    })

    // Save draft to database
    try {
      // Ensure manager exists in appy_managers table
      await db.ensureManagerExists(session.userId, session.fullName, session.email)
      
      await db.saveAppraisalDraft({
        periodId: currentPeriod.id,
        employeeId: draft.employeeId,
        managerId: session.userId,
        question1: draft.q1 || null,
        question2: draft.q2 !== undefined ? String(draft.q2) : null,
        question3: draft.q3 || null,
        question4: draft.q4 || null,
        question5: draft.q5 || null,
      })

      console.log("Draft saved successfully to database.")
    } catch (dbError) {
      console.error("Database error during draft save:", dbError)
      // Don't throw error for draft saves - just log and continue
      console.log("Draft save failed, but continuing...")
    }

    return { success: true, message: "Draft saved successfully." }
  } catch (error) {
    return handleServerActionError(error)
  }
}

export async function submitAppraisalAction(appraisal: Partial<AppraisalDetails>) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting (stricter for submissions)
    if (!checkRateLimit(session.userId, 'appraisal-submit', 5, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('appraisal:write')

    // Validate submission data (strict validation)
    const submissionData = {
      ...appraisal,
      managerId: session.userId, // Set managerId from authenticated session
      status: 'submitted' as const,
    }

    console.log('📋 [DEBUG] Submission data before validation:', JSON.stringify(submissionData, null, 2))

    const validatedData = validateAppraisalSubmission(submissionData)

    console.log('✅ [DEBUG] Validated submission data:', JSON.stringify(validatedData, null, 2))

    // Additional business logic validation
    if (!validatedData.employeeId) {
      throw new ValidationError("Employee ID is required")
    }

    // Log the action
    await logUserAction('appraisal:submit', {
      employeeId: validatedData.employeeId,
      managerId: session.userId,
      submittedAt: new Date().toISOString()
    })

    console.log(`Submitting appraisal for employee ${validatedData.employeeId}`)

    // Get current period (in real app, this would be more sophisticated)
    const periods = await getPeriods()
    const currentPeriod = periods.find(p => !p.closed)

    if (!currentPeriod) {
      throw new ValidationError("No active appraisal period found")
    }

    // Save or update the appraisal in the database
    try {
      // Ensure manager exists in appy_managers table
      await db.ensureManagerExists(session.userId, session.fullName, session.email)
      
      // First, save as draft to ensure the record exists
      await db.saveAppraisalDraft({
        periodId: currentPeriod.id,
        employeeId: validatedData.employeeId,
        managerId: session.userId,
        question1: validatedData.q1,
        question2: validatedData.q2 !== undefined ? String(validatedData.q2) : null,
        question3: validatedData.q3,
        question4: validatedData.q4,
        question5: validatedData.q5,
      })

      // Get the appraisal record to get its ID
      const appraisalRecord = await db.getAppraisalByEmployeeId(
        validatedData.employeeId,
        currentPeriod.id
      )

      if (!appraisalRecord) {
        throw new Error("Failed to create appraisal record")
      }

      // Now submit it
      await db.submitAppraisal(appraisalRecord.id)

      console.log("Appraisal submitted successfully to database.")
    } catch (dbError) {
      console.error("Database error during appraisal submission:", dbError)
      throw new Error("Failed to save appraisal to database")
    }

    // Invalidate performance stats cache
    invalidatePerformanceStatsCache(session.userId)

    // Revalidate relevant pages
    revalidatePath("/dashboard")
    revalidatePath(`/dashboard/appraisal/${validatedData.employeeId}`)
    revalidatePath("/dashboard/approvals")

    return { success: true, message: "Appraisal submitted successfully." }
  } catch (error) {
    return handleServerActionError(error)
  }
}

// Create Appraisal Revision Action
export async function createAppraisalRevisionAction(appraisalId: string) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'appraisal-revision', 3, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('appraisal:write')

    // Validate appraisal ID
    if (!appraisalId || typeof appraisalId !== 'string') {
      throw new ValidationError("Valid appraisal ID is required")
    }

    // Create the revision in the database
    try {
      const revisedAppraisal = await db.createAppraisalRevision(appraisalId, {})
      
      console.log(`Created revision for appraisal ${appraisalId}, new revision number: ${revisedAppraisal.revision_number}`)

      // Log the action
      await logUserAction('appraisal:revision', {
        appraisalId,
        revisionNumber: revisedAppraisal.revision_number,
        managerId: session.userId
      })

      // Revalidate relevant pages
      revalidatePath("/dashboard")
      revalidatePath(`/dashboard/appraisal/${revisedAppraisal.employee_id}`)
      revalidatePath("/dashboard/approvals")

      return { 
        success: true, 
        message: "Appraisal revision created successfully. You can now edit and resubmit it.",
        revisionNumber: revisedAppraisal.revision_number
      }
    } catch (dbError) {
      console.error("Database error during revision creation:", dbError)
      throw new Error("Failed to create appraisal revision")
    }
  } catch (error) {
    return handleServerActionError(error)
  }
}

// Resubmit Appraisal Revision Action
export async function resubmitAppraisalRevisionAction(appraisal: Partial<AppraisalDetails>) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting (stricter for submissions)
    if (!checkRateLimit(session.userId, 'appraisal-resubmit', 5, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('appraisal:write')

    // Validate submission data (strict validation)
    const submissionData = {
      ...appraisal,
      managerId: session.userId,
      status: 'submitted' as const,
    }

    const validatedData = validateAppraisalSubmission(submissionData)

    // Additional business logic validation
    if (!validatedData.employeeId || !validatedData.id) {
      throw new ValidationError("Employee ID and Appraisal ID are required")
    }

    // Log the action
    await logUserAction('appraisal:resubmit', {
      appraisalId: validatedData.id,
      employeeId: validatedData.employeeId,
      managerId: session.userId,
      resubmittedAt: new Date().toISOString()
    })

    console.log(`Resubmitting appraisal revision ${validatedData.id} for employee ${validatedData.employeeId}`)

    // Save the updated draft data first, then resubmit
    try {
      // Update the appraisal with current data
      await db.saveAppraisalDraft({
        periodId: validatedData.periodId,
        employeeId: validatedData.employeeId,
        managerId: session.userId,
        question1: validatedData.q1,
        question2: validatedData.q2 !== undefined ? String(validatedData.q2) : null,
        question3: validatedData.q3,
        question4: validatedData.q4,
        question5: validatedData.q5,
      })

      // Get the updated appraisal record
      const periods = await getPeriods()
      const currentPeriod = periods.find(p => !p.closed)
      
      if (!currentPeriod) {
        throw new ValidationError("No active appraisal period found")
      }

      const appraisalRecord = await db.getAppraisalByEmployeeId(
        validatedData.employeeId,
        currentPeriod.id
      )

      if (!appraisalRecord) {
        throw new Error("Appraisal record not found")
      }

      // Resubmit the revision
      await db.resubmitAppraisalRevision(appraisalRecord.id)

      console.log("Appraisal revision resubmitted successfully to database.")
    } catch (dbError) {
      console.error("Database error during revision resubmission:", dbError)
      throw new Error("Failed to resubmit appraisal revision")
    }

    // Invalidate performance stats cache
    invalidatePerformanceStatsCache(session.userId)

    // Revalidate relevant pages
    revalidatePath("/dashboard")
    revalidatePath(`/dashboard/appraisal/${validatedData.employeeId}`)
    revalidatePath("/dashboard/approvals")

    return { success: true, message: "Appraisal revision resubmitted successfully." }
  } catch (error) {
    return handleServerActionError(error)
  }
}

// Additional secure action for deleting entities
export async function deleteEmployeeAction(employeeId: string) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'employee-delete', 3, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check (only super-admin and hr-admin can delete)
    await requirePermission('employee:delete')

    // Validate employee ID
    if (!employeeId || typeof employeeId !== 'string') {
      throw new ValidationError("Valid employee ID is required")
    }

    // Log the action
    await logUserAction('employee:delete', { employeeId })

    // TODO: In real implementation, soft delete employee
    // await softDeleteEmployee(employeeId)

    // Revalidate cache
    revalidatePath("/dashboard/employees")

    return { success: true, message: "Employee deleted successfully." }
  } catch (error) {
    return handleServerActionError(error)
  }
}

// Bulk action for exporting data
export async function exportAppraisalsAction(filters?: {
  departmentId?: string
  periodId?: string
  status?: 'submitted' | 'draft'
}) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting (stricter for exports)
    if (!checkRateLimit(session.userId, 'export-data', 2, 300000)) { // 2 per 5 minutes
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('approval:export')

    // Log the action
    await logUserAction('data:export', {
      filters,
      exportType: 'appraisals'
    })

    // TODO: In real implementation, generate and return export data
    // const exportData = await generateAppraisalExport(filters)

    return {
      success: true,
      message: "Export generated successfully.",
      // data: exportData
    }
  } catch (error) {
    return handleServerActionError(error)
  }
}

// PTO (Paid Time Off) Server Actions
export async function submitPTORequestAction(formData: FormData) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'pto-request', 5, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('pto:write')

    // Extract and validate data
    const rawData = {
      requestType: formData.get('requestType') as string,
      startDate: formData.get('startDate') as string,
      endDate: formData.get('endDate') as string,
      daysRequested: Number(formData.get('daysRequested')),
      reason: formData.get('reason') as string || undefined,
    }

    // Import PTO validation function
    const { validatePTORequestForm } = await import('./schemas')
    const validatedData = validatePTORequestForm(rawData)

    // Get current user's employee record
    const { getEmployees } = await import('./data')
    const employees = await getEmployees()
    const employee = employees.find(emp => emp.managerId === session.userId)

    if (!employee) {
      throw new ValidationError('No employee record found for current user')
    }

    // Check PTO availability
    const { checkPTOAvailability } = await import('./data')
    const hasAvailableDays = await checkPTOAvailability(employee.id, validatedData.daysRequested)
    
    if (!hasAvailableDays) {
      throw new ValidationError('Insufficient PTO balance for this request')
    }

    // Get employee's manager
    if (!employee.managerId) {
      throw new ValidationError('No manager assigned to employee')
    }

    // Create PTO request
    const { db } = await import('./db')
    await db.createPTORequest({
      employeeId: employee.id,
      managerId: employee.managerId,
      requestType: validatedData.requestType as 'vacation' | 'sick' | 'personal' | 'emergency',
      startDate: validatedData.startDate,
      endDate: validatedData.endDate,
      daysRequested: validatedData.daysRequested,
      reason: validatedData.reason,
    })

    // Log the action
    await logUserAction('pto:request', {
      employeeId: employee.id,
      requestType: validatedData.requestType,
      daysRequested: validatedData.daysRequested,
      startDate: validatedData.startDate,
      endDate: validatedData.endDate,
    })

    // Invalidate PTO cache
    const { invalidatePTOCache } = await import('./data')
    invalidatePTOCache(employee.managerId)

    // Revalidate relevant pages
    revalidatePath('/dashboard/pto')

    return { success: true, message: 'PTO request submitted successfully.' }
  } catch (error) {
    return handleServerActionError(error)
  }
}

export async function approvePTORequestAction(requestId: string, action: 'approve' | 'reject', rejectedReason?: string) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'pto-approval', 10, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('pto:approve')

    // Validate approval data
    const { validatePTOApproval } = await import('./schemas')
    const validatedData = validatePTOApproval({
      requestId,
      action,
      rejectedReason
    })

    // Get the PTO request to verify manager can approve it
    const { db } = await import('./db')
    const ptoRequest = await db.getPTORequestById(requestId)

    if (!ptoRequest) {
      throw new ValidationError('PTO request not found')
    }

    // Check if current user is the manager of the employee
    if (ptoRequest.manager_id !== session.userId) {
      throw new AuthorizationError('You can only approve requests from your team members')
    }

    // Check if request is still pending
    if (ptoRequest.status !== 'pending') {
      throw new ValidationError('This request has already been processed')
    }

    // Approve or reject the request
    if (validatedData.action === 'approve') {
      await db.approvePTORequest(requestId, session.userId)
    } else {
      await db.rejectPTORequest(requestId, session.userId, validatedData.rejectedReason!)
    }

    // Log the action
    await logUserAction(`pto:${validatedData.action}`, {
      requestId,
      employeeId: ptoRequest.employee_id,
      managerId: session.userId,
      daysRequested: ptoRequest.days_requested,
      rejectedReason: validatedData.rejectedReason,
    })

    // Invalidate PTO cache
    const { invalidatePTOCache } = await import('./data')
    invalidatePTOCache(session.userId)

    // Revalidate relevant pages
    revalidatePath('/dashboard/pto')

    const actionMessage = validatedData.action === 'approve' ? 'approved' : 'rejected'
    return { success: true, message: `PTO request ${actionMessage} successfully.` }
  } catch (error) {
    return handleServerActionError(error)
  }
}

export async function cancelPTORequestAction(requestId: string) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'pto-cancel', 10, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('pto:write')

    // Get the PTO request to verify user can cancel it
    const { db } = await import('./db')
    const ptoRequest = await db.getPTORequestById(requestId)

    if (!ptoRequest) {
      throw new ValidationError('PTO request not found')
    }

    // Check if current user is the employee who made the request
    const { getEmployees } = await import('./data')
    const employees = await getEmployees()
    const employee = employees.find(emp => emp.id === ptoRequest.employee_id)

    if (!employee || employee.managerId !== session.userId) {
      throw new AuthorizationError('You can only cancel your own requests')
    }

    // Check if request can be cancelled (only pending requests)
    if (ptoRequest.status !== 'pending') {
      throw new ValidationError('Only pending requests can be cancelled')
    }

    // Cancel the request
    await db.cancelPTORequest(requestId)

    // Log the action
    await logUserAction('pto:cancel', {
      requestId,
      employeeId: ptoRequest.employee_id,
      managerId: session.userId,
    })

    // Invalidate PTO cache
    const { invalidatePTOCache } = await import('./data')
    invalidatePTOCache(ptoRequest.manager_id)

    // Revalidate relevant pages
    revalidatePath('/dashboard/pto')

    return { success: true, message: 'PTO request cancelled successfully.' }
  } catch (error) {
    return handleServerActionError(error)
  }
}

export async function initializePTOBalancesAction() {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting (stricter for admin actions)
    if (!checkRateLimit(session.userId, 'pto-init', 1, 300000)) { // 1 per 5 minutes
      throw new RateLimitError()
    }

    // Authorization check (only super-admin can initialize)
    await requirePermission('*')

    // Initialize PTO balances for Marketing department
    const { initializeMarketingDepartmentPTO } = await import('./data')
    await initializeMarketingDepartmentPTO()

    // Log the action
    await logUserAction('pto:initialize', {
      department: 'Marketing',
      adminId: session.userId,
    })

    return { success: true, message: 'PTO balances initialized successfully for Marketing department.' }
  } catch (error) {
    return handleServerActionError(error)
  }
}

// Global Search Server Action
export async function searchAllEntities(query: string) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'search', 30, 60000)) { // 30 per minute
      throw new RateLimitError()
    }

    if (!query.trim()) {
      return []
    }

    const searchTerm = query.toLowerCase().trim()
    const results: Array<{
      id: string
      title: string
      subtitle?: string
      href: string
      type: "employee" | "department" | "manager" | "period" | "navigation" | "pto"
      icon: string
    }> = []

    // Import data functions
    const { 
      getEmployees, 
      getDepartments, 
      getManagers, 
      getPeriods,
      getPTORequestsForManager,
    } = await import('./data')

    // Import auth function
    const { getCurrentUser } = await import('./auth')
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return []
    }

    // Search employees (if user has access)
    if (['hr-admin', 'admin', 'super-admin', 'manager'].includes(currentUser.role)) {
      const employees = await getEmployees()
      console.log('🔍 [SEARCH DEBUG] Available employees:', employees.map(emp => emp.fullName))
      console.log('🔍 [SEARCH DEBUG] Search term:', searchTerm)

      const matchingEmployees = employees
        .filter(emp =>
          emp.fullName.toLowerCase().includes(searchTerm) ||
          emp.departmentName?.toLowerCase().includes(searchTerm)
        )
        .slice(0, 5)

      console.log('🔍 [SEARCH DEBUG] Matching employees:', matchingEmployees.map(emp => emp.fullName))

      const employeeResults = matchingEmployees.map(emp => {
        const appraisalHref = `/dashboard/appraisal/${emp.id}`
        console.log(`🎯 [APPRAISAL LINK DEBUG] Employee ${emp.fullName} (ID: ${emp.id}) -> ${appraisalHref}`)

        return {
          id: `employee-${emp.id}`,
          title: emp.fullName,
          subtitle: `${emp.departmentName} • ${emp.compensation === 'hourly' ? 'Hourly' : 'Monthly'}`,
          href: appraisalHref,
          type: "employee" as const,
          icon: "user"
        }
      })

      console.log('🔍 [SEARCH DEBUG] Employee results to add:', employeeResults)
      results.push(...employeeResults)
    }

    // Search departments (if user has access)
    if (['hr-admin', 'admin', 'super-admin'].includes(currentUser.role)) {
      const departments = await getDepartments()
      const matchingDepartments = departments
        .filter(dept => dept.name.toLowerCase().includes(searchTerm))
        .slice(0, 3)

      results.push(...matchingDepartments.map(dept => ({
        id: `department-${dept.id}`,
        title: dept.name,
        subtitle: "Department",
        href: "/dashboard/departments",
        type: "department" as const,
        icon: "building2"
      })))
    }

    // Search managers (if user has access)
    if (['hr-admin', 'admin', 'super-admin'].includes(currentUser.role)) {
      const managers = await getManagers()
      const matchingManagers = managers
        .filter(mgr => mgr.fullName.toLowerCase().includes(searchTerm))
        .slice(0, 3)

      results.push(...matchingManagers.map(mgr => ({
        id: `manager-${mgr.id}`,
        title: mgr.fullName,
        subtitle: "Manager",
        href: "/dashboard/employees",
        type: "manager" as const,
        icon: "user-check"
      })))
    }

    // Search appraisal periods (if user has access)
    if (['hr-admin', 'admin', 'super-admin', 'manager'].includes(currentUser.role)) {
      const periods = await getPeriods()
      const matchingPeriods = periods
        .filter(period => {
          const startDate = new Date(period.periodStart).toLocaleDateString()
          const endDate = new Date(period.periodEnd).toLocaleDateString()
          return startDate.includes(searchTerm) || 
                 endDate.includes(searchTerm) ||
                 period.id.toLowerCase().includes(searchTerm)
        })
        .slice(0, 2)

      results.push(...matchingPeriods.map(period => ({
        id: `period-${period.id}`,
        title: `${new Date(period.periodStart).toLocaleDateString()} - ${new Date(period.periodEnd).toLocaleDateString()}`,
        subtitle: period.closed ? "Closed Period" : "Active Period",
        href: "/dashboard/periods",
        type: "period" as const,
        icon: "calendar"
      })))
    }

    // Search PTO requests (if user has access and is Marketing department)
    if (currentUser.role === 'super-admin' || 
        (currentUser.fullName?.toLowerCase().includes('natalie') ||
         currentUser.id === 'user_natalie')) {
      
      if (['manager', 'hr-admin', 'admin', 'super-admin'].includes(currentUser.role)) {
        try {
          const ptoRequests = await getPTORequestsForManager(currentUser.id)
          const matchingPTO = ptoRequests
            .filter(pto => 
              pto.employeeName?.toLowerCase().includes(searchTerm) ||
              pto.status?.toLowerCase().includes(searchTerm) ||
              pto.requestType?.toLowerCase().includes(searchTerm)
            )
            .slice(0, 3)

          results.push(...matchingPTO.map(pto => ({
            id: `pto-${pto.id}`,
            title: `${pto.employeeName} - ${pto.requestType}`,
            subtitle: `${pto.status} • ${pto.daysRequested} days`,
            href: "/dashboard/pto",
            type: "pto" as const,
            icon: "calendar-days"
          })))
        } catch (error) {
          // PTO search failed, continue without PTO results
        }
      }
    }

    // Add navigation items that match
    const navigationItems = [
      { title: "Dashboard", href: "/dashboard", subtitle: "Main overview", roles: ["manager", "accountant", "hr-admin", "admin", "super-admin"] },
      { title: "Employees", href: "/dashboard/employees", subtitle: "Manage employee records", roles: ["hr-admin", "admin", "super-admin"] },
      { title: "Departments", href: "/dashboard/departments", subtitle: "Manage departments", roles: ["hr-admin", "admin", "super-admin"] },
      { title: "Appraisal Periods", href: "/dashboard/periods", subtitle: "Manage appraisal periods", roles: ["hr-admin", "admin", "super-admin"] },
      { title: "Reports", href: "/dashboard/reports", subtitle: "View analytics and reports", roles: ["manager", "hr-admin", "admin", "super-admin"] },
      { title: "PTO", href: "/dashboard/pto", subtitle: "Time off management", roles: ["manager", "accountant", "hr-admin", "admin", "super-admin"] },
      { title: "System Admin", href: "/dashboard/admin", subtitle: "System administration", roles: ["super-admin"] },
    ]

    const matchingNavigation = navigationItems
      .filter(item => 
        item.roles.includes(currentUser.role) &&
        (item.title.toLowerCase().includes(searchTerm) || 
         item.subtitle.toLowerCase().includes(searchTerm))
      )
      .slice(0, 3)

    results.push(...matchingNavigation.map(nav => ({
      id: `nav-${nav.href}`,
      title: nav.title,
      subtitle: nav.subtitle,
      href: nav.href,
      type: "navigation" as const,
      icon: "navigation"
    })))

    // Log search action (for analytics, rate limiting)
    await logUserAction('search:query', {
      query: searchTerm,
      resultCount: results.length,
      userId: session.userId,
    })

    console.log('🔍 [SEARCH DEBUG] Final results before sorting:', results)

    // Sort results by relevance (exact matches first, then partial)
    const finalResults = results
      .sort((a, b) => {
        const aExact = a.title.toLowerCase() === searchTerm ? 1 : 0
        const bExact = b.title.toLowerCase() === searchTerm ? 1 : 0
        return bExact - aExact
      })
      .slice(0, 15) // Limit total results

    console.log('🔍 [SEARCH DEBUG] Final results being returned:', finalResults)
    return finalResults

  } catch (error) {
    console.error('Search failed:', error)
    return []
  }
}
